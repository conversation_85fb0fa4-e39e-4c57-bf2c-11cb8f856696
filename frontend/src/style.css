/* Chateo 移动端聊天应用全局样式 */

/* ===== CSS Reset ===== */
/* 基于现代 CSS Reset 最佳实践 */

/* Box sizing rules */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margin and padding */
* {
  margin: 0;
  padding: 0;
}

/* Remove list styles on ul, ol elements with a list role */
ul[role='list'],
ol[role='list'] {
  list-style: none;
}

/* Set core root defaults */
html:focus-within {
  scroll-behavior: smooth;
}

/* Set core body defaults */
html,
body {
  height: 100%;
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-background-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeSpeed;
}

/* A elements that don't have a class get default styles */
a:not([class]) {
  text-decoration-skip-ink: auto;
}

/* Make images easier to work with */
img,
picture,
svg {
  max-width: 100%;
  display: block;
}

/* Remove all animations, transitions and smooth scroll for people that prefer not to see them */
/* 但保留页面过渡动画 */
@media (prefers-reduced-motion: reduce) {
  html:focus-within {
   scroll-behavior: auto;
  }

  *:not([class*="page-"]),
  *:not([class*="page-"])::before,
  *:not([class*="page-"])::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== 应用基础样式 ===== */

/* 移除移动端点击时的蓝色背景 */
* {
  -webkit-tap-highlight-color: transparent;
}

#app {
  height: 100%;
  overflow: hidden;
  background: var(--color-background-primary);
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background-primary);
}

/* ===== 响应式字体大小 ===== */
@media screen and (max-width: 375px) {
  html {
    font-size: var(--font-size-sm);
  }
}

@media screen and (min-width: 768px) {
  html {
    font-size: var(--font-size-base);
  }
}

/* ===== 滚动条样式 ===== */
::-webkit-scrollbar {
  width: var(--space-1);
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* Firefox 滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray-300) transparent;
}

/* ===== 安全区域适配 ===== */
.safe-area-inset-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* ===== 头像组件修复 ===== */
/* 确保头像图片和初始字母容器尺寸一致 */
.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar img,
.avatar-initials {
  width: var(--avatar-size-lg);
  height: var(--avatar-size-lg);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.avatar img {
  object-fit: cover;
}

.avatar-initials {
  color: var(--color-text-inverse);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  text-transform: uppercase;
  background: var(--color-primary-500);
}

/* ===== 通用工具类 ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* ===== 打印样式 ===== */
@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  img {
    page-break-inside: avoid;
  }
  
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  
  h2,
  h3 {
    page-break-after: avoid;
  }
}
