<template>
  <div class="chat-list">
    <header class="app-bar">
      <div class="header-content">
        <h1>Chats</h1>
        <div class="header-actions">
          <button class="action-btn">
            <Plus />
          </button>
        </div>
      </div>
    </header>
    
    <!-- 搜索栏 -->
    <div class="search-section">
      <div class="search-container">
        <Search class="search-icon" />
        <input 
          type="text" 
          class="search-input" 
          placeholder="Search chats..."
          v-model="searchQuery"
          @input="handleSearch"
        />
      </div>
    </div>
    
    <!-- 聊天列表 -->
    <div class="chat-content">
      <div v-if="chatList.length === 0" class="empty-state">
        <div class="empty-icon">
          <MessageCircle color="#6c757d" />
        </div>
        <h3 class="empty-title">No chats yet</h3>
        <p class="empty-text">Start a conversation with your contacts</p>
      </div>
      
      <div v-else class="chat-items">
        <div v-for="chat in filteredChatList" :key="chat.id" class="chat-item" @click="openChat(chat)">
          <div class="avatar">
            <img 
              :src="chat.avatar" 
              :alt="chat.name" 
              @error="handleImageError"
              v-show="!chat.showInitials"
            />
            <div 
              v-show="chat.showInitials" 
              class="avatar-initials"
              :style="{ backgroundColor: getAvatarColor(chat.name) }"
            >
              {{ getInitials(chat.name) }}
            </div>
            <div v-if="chat.isOnline" class="online-indicator"></div>
          </div>
          <div class="chat-info">
            <div class="chat-header">
              <h3 class="chat-name">{{ chat.name }}</h3>
              <span class="chat-time">{{ formatTime(chat.lastMessageTime) }}</span>
            </div>
            <div class="chat-preview">
              <p class="last-message">{{ chat.lastMessage }}</p>
              <div v-if="chat.unreadCount > 0" class="unread-badge">{{ chat.unreadCount }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineOptions } from 'vue'
import { useRouter } from 'vue-router'
import { Search, Plus, MessageCircle } from 'lucide-vue-next'

// 定义组件名称，用于 KeepAlive
defineOptions({
  name: 'ChatList'
})

const router = useRouter()

// 搜索相关
const searchQuery = ref('')

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑将在这里实现
}

// 过滤后的聊天列表
const filteredChatList = computed(() => {
  if (!searchQuery.value.trim()) {
    return chatList.value
  }
  return chatList.value.filter(chat => 
    chat.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    chat.lastMessage.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 聊天列表数据
const chatList = ref([
  {
    id: 1,
    name: 'Alice Johnson',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    lastMessage: 'Hey! How are you doing?',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
    unreadCount: 2,
    isOnline: true,
    showInitials: false
  },
  {
    id: 2,
    name: 'Bob Smith',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    lastMessage: 'See you tomorrow!',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    unreadCount: 0,
    isOnline: false,
    showInitials: false
  },
  {
    id: 3,
    name: 'Carol Davis',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    lastMessage: 'Thanks for your help!',
    lastMessageTime: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    unreadCount: 1,
    isOnline: true,
    showInitials: false
  }
])

// 打开聊天
const openChat = (chat: any) => {
  router.push(`/chat/${chat.id}`)
}

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return 'now'
  if (minutes < 60) return `${minutes}m`
  if (hours < 24) return `${hours}h`
  if (days < 7) return `${days}d`
  
  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  const chatItem = chatList.value.find(chat => chat.avatar === target.src)
  if (chatItem) {
    chatItem.showInitials = true
  }
}

// 获取用户名首字母
const getInitials = (name: string) => {
  return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2)
}

// 根据用户名生成头像颜色
const getAvatarColor = (name: string) => {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ]
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash)
  }
  return colors[Math.abs(hash) % colors.length]
}
</script>

<style scoped>
.chat-list {
  height: 100%;
  background: var(--color-background-primary);
  display: flex;
  flex-direction: column;
}

.app-bar {
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
  padding: var(--space-3) var(--space-4);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  box-shadow: var(--shadow-xs);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-bar h1 {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  letter-spacing: var(--letter-spacing-tight);
}

.header-actions {
  display: flex;
  gap: var(--space-2);
}

.action-btn {
  width: var(--button-height-sm);
  height: var(--button-height-sm);
  border: none;
  background: var(--color-background-secondary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  box-shadow: var(--shadow-sm);
}

.action-btn:hover {
  background: var(--color-background-tertiary);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.search-section {
  padding: var(--space-3) var(--space-4);
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  color: var(--color-text-tertiary);
  z-index: 1;
}

.search-input {
  width: 100%;
  height: var(--input-height-sm);
  padding: 0 var(--space-3) 0 var(--space-10);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-full);
  background: var(--color-background-secondary);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  outline: none;
  transition: all var(--duration-fast) var(--ease-out);
}

.search-input::placeholder {
  color: var(--color-text-tertiary);
}

.search-input:focus {
  border-color: var(--color-border-focus);
  background: var(--color-background-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.chat-content {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--space-10) var(--space-5);
  text-align: center;
}

.empty-icon {
  margin-bottom: var(--space-6);
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-2) 0;
}

.empty-text {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.chat-items {
  padding: var(--space-2) 0;
}

.chat-item {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  border-bottom: 1px solid var(--color-border-primary);
  margin: 0 var(--space-3);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-1);
}

.chat-item:hover {
  background: var(--color-background-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.chat-item:last-child {
  border-bottom: none;
}

.avatar {
  position: relative;
  margin-right: var(--space-3);
  flex-shrink: 0;
}

.avatar img {
  width: var(--avatar-size-lg);
  height: var(--avatar-size-lg);
  border-radius: var(--radius-xl);
  object-fit: cover;
  box-shadow: var(--shadow-sm);
}

.avatar-initials {
  width: var(--avatar-size-lg);
  height: var(--avatar-size-lg);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-inverse);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  text-transform: uppercase;
  box-shadow: var(--shadow-sm);
}

.online-indicator {
  position: absolute;
  bottom: var(--space-1);
  right: var(--space-1);
  width: var(--space-3);
  height: var(--space-3);
  background: var(--color-success);
  border: 2px solid var(--color-background-primary);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-xs);
}

.chat-info {
  flex: 1;
  min-width: 0;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-1);
}

.chat-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: var(--letter-spacing-tight);
}

.chat-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  flex-shrink: 0;
  margin-left: var(--space-2);
  font-weight: var(--font-weight-medium);
}

.chat-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.last-message {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  line-height: var(--line-height-tight);
}

.unread-badge {
  background: var(--color-primary-500);
  color: var(--color-text-inverse);
  border-radius: var(--radius-lg);
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: var(--space-2);
  flex-shrink: 0;
  box-shadow: var(--shadow-xs);
}

/* 图标样式 */
.action-btn svg {
  width: 20px;
  height: 20px;
  transition: all 0.2s ease-in-out;
}

.action-btn:hover svg {
  width: 22px;
  height: 22px;
}

.search-icon {
  width: 20px;
  height: 20px;
}

.empty-icon svg {
  width: 64px;
  height: 64px;
}

</style>