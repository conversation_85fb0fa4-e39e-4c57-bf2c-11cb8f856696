<template>
  <div class="chat-detail">
    <!-- 顶部导航栏 -->
    <header class="chat-header">
      <button class="back-btn" @click="$router.back()">
        <ChevronLeft />
      </button>
      <div class="contact-info">
        <div class="avatar">
          <img 
            :src="contact.avatar" 
            :alt="contact.name" 
            @error="handleImageError"
            v-show="!contact.showInitials"
          />
          <div 
            v-show="contact.showInitials" 
            class="avatar-initials"
          >
            {{ getInitials(contact.name) }}
          </div>
          <div v-if="contact.isOnline" class="online-indicator"></div>
        </div>
        <div class="contact-details">
          <h1 class="contact-name">{{ contact.name }}</h1>
          <p class="contact-status">{{ contact.isOnline ? 'Online' : 'Last seen recently' }}</p>
        </div>
      </div>
      <div class="header-actions">
        <button class="action-btn">
          <Search />
        </button>
        <button class="action-btn">
          <MoreHorizontal />
        </button>
      </div>
    </header>

    <!-- 聊天消息区域 -->
    <div class="chat-content" ref="chatContent">
      <div class="messages-container">
        <!-- 日期分隔符 -->
        <div class="date-divider">
          <div class="divider-line"></div>
          <span class="divider-text">Sat, 17/10</span>
          <div class="divider-line"></div>
        </div>

        <!-- 消息列表 -->
        <div class="messages-list">
          <!-- 接收的消息（带图片） -->
          <div class="message-group received">
            <div class="message-bubble received">
              <div class="message-image">
                <img src="https://images.unsplash.com/photo-1574158622682-e40e69881006?w=200&h=150&fit=crop" alt="Shared image" />
              </div>
              <p class="message-text">Look at how chocho sleep in my arms!</p>
              <span class="message-time">16.46</span>
            </div>
          </div>

          <!-- 接收的消息（回复） -->
          <div class="message-group received">
            <div class="message-bubble received">
              <div class="reply-context">
                <div class="reply-indicator"></div>
                <div class="reply-content">
                  <span class="reply-author">You</span>
                  <p class="reply-text">Can I come over?</p>
                </div>
              </div>
              <p class="message-text">Of course, let me know if you're on your way</p>
              <span class="message-time">16.46</span>
            </div>
          </div>

          <!-- 发送的消息 -->
          <div class="message-group sent">
            <div class="message-bubble sent">
              <p class="message-text">K, I'm on my way</p>
              <span class="message-time">16.50 · Read</span>
            </div>
          </div>

          <!-- 发送的语音消息 -->
          <div class="message-group sent">
            <div class="message-bubble sent">
              <div class="voice-message">
                <div class="voice-controls">
                  <button class="play-btn">
                    <Play fill="white" />
                  </button>
                  <span class="voice-duration">0:20</span>
                </div>
                <div class="voice-waveform">
                  <div class="wave-bar" v-for="n in 17" :key="n" :style="{ height: getWaveHeight(n) }"></div>
                </div>
              </div>
              <span class="message-time">09.13 · Read</span>
            </div>
          </div>

          <!-- 接收的消息 -->
          <div class="message-group received">
            <div class="message-bubble received">
              <p class="message-text">Good morning, did you sleep well?</p>
              <span class="message-time">09.45</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-container">
        <button class="attachment-btn">
          <Plus />
        </button>
        <div class="message-input-wrapper">
          <input 
            type="text" 
            class="message-input" 
            :placeholder="inputPlaceholder"
            v-model="messageText"
            @keyup.enter="sendMessage"
            @input="handleTyping"
          />
        </div>
        <button class="send-btn" @click="sendMessage" :disabled="!messageText.trim()">
          <Send />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, defineOptions } from 'vue'
import { useRouter } from 'vue-router'
import { ChevronLeft, Search, MoreHorizontal, Plus, Send, Play } from 'lucide-vue-next'

// 定义组件名称，用于 KeepAlive
defineOptions({
  name: 'ChatDetail'
})

const router = useRouter()
const chatContent = ref<HTMLElement>()
const messageText = ref('')
const inputPlaceholder = ref('Good mor|')

// 联系人信息
const contact = ref({
  id: 1,
  name: 'Alice Johnson',
  avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
  isOnline: true,
  showInitials: false
})

// 处理头像加载错误
const handleImageError = () => {
  contact.value.showInitials = true
}

// 获取姓名首字母
const getInitials = (name: string) => {
  return name.split(' ').map(n => n[0]).join('').toUpperCase()
}

// 获取语音波形高度
const getWaveHeight = (index: number) => {
  const heights = ['8px', '12px', '16px', '10px', '14px', '18px', '12px', '16px', '20px', '14px', '18px', '16px', '12px', '10px', '14px', '12px', '8px']
  return heights[index - 1] || '8px'
}

// 发送消息
const sendMessage = () => {
  if (!messageText.value.trim()) return
  
  // TODO: 实现发送消息逻辑
  console.log('Sending message:', messageText.value)
  messageText.value = ''
  
  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

// 处理输入
const handleTyping = () => {
  // TODO: 实现输入状态逻辑
}

// 滚动到底部
const scrollToBottom = () => {
  if (chatContent.value) {
    chatContent.value.scrollTop = chatContent.value.scrollHeight
  }
}

// 组件挂载后滚动到底部
onMounted(() => {
  nextTick(() => {
    scrollToBottom()
  })
})
</script>

<style scoped>
.chat-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--color-background-primary);
}

/* 顶部导航栏 */
.chat-header {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
  gap: var(--space-3);
  min-height: 64px;
}

.back-btn {
  width: var(--button-height-sm);
  height: var(--button-height-sm);
  border: none;
  background: transparent;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-primary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
}

.back-btn:hover {
  background: var(--color-background-secondary);
}

.contact-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
}

.avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar img,
.avatar-initials {
  width: var(--avatar-size-md);
  height: var(--avatar-size-md);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.avatar img {
  object-fit: cover;
}

.avatar-initials {
  color: var(--color-text-inverse);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  background: var(--color-primary-500);
}

.online-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: var(--space-3);
  height: var(--space-3);
  background: var(--color-success);
  border: 2px solid var(--color-background-primary);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-xs);
}

.contact-details {
  flex: 1;
  min-width: 0;
}

.contact-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-1) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contact-status {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--space-2);
}

.action-btn {
  width: var(--button-height-sm);
  height: var(--button-height-sm);
  border: none;
  background: transparent;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
}

.action-btn:hover {
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
}

/* 聊天内容区域 */
.chat-content {
  flex: 1;
  overflow-y: auto;
  background: var(--color-background-primary);
}

.messages-container {
  padding: var(--space-4);
  max-width: 100%;
}

/* 日期分隔符 */
.date-divider {
  display: flex;
  align-items: center;
  margin: var(--space-6) 0;
  gap: var(--space-3);
}

.divider-line {
  flex: 1;
  height: 1px;
  background: var(--color-border-secondary);
}

.divider-text {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
  padding: 0 var(--space-2);
  background: var(--color-background-primary);
}

/* 消息列表 */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.message-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.message-group.received {
  align-items: flex-start;
}

.message-group.sent {
  align-items: flex-end;
}

/* 消息气泡 */
.message-bubble {
  max-width: 280px;
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  position: relative;
  word-wrap: break-word;
}

.message-bubble.received {
  background: var(--color-background-secondary);
  border-bottom-left-radius: var(--radius-sm);
}

.message-bubble.sent {
  background: var(--color-primary-500);
  border-bottom-right-radius: var(--radius-sm);
}

.message-text {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  margin: 0 0 var(--space-2) 0;
  word-wrap: break-word;
}

.message-bubble.received .message-text {
  color: var(--color-text-primary);
}

.message-bubble.sent .message-text {
  color: var(--color-text-inverse);
}

.message-time {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  opacity: 0.7;
}

.message-bubble.received .message-time {
  color: var(--color-text-tertiary);
}

.message-bubble.sent .message-time {
  color: var(--color-text-inverse);
}

/* 消息图片 */
.message-image {
  margin-bottom: var(--space-2);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.message-image img {
  width: 100%;
  height: auto;
  display: block;
}

/* 回复消息 */
.reply-context {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
  padding: var(--space-2);
  background: var(--color-background-tertiary);
  border-radius: var(--radius-sm);
}

.reply-indicator {
  width: 3px;
  background: var(--color-primary-500);
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.reply-content {
  flex: 1;
  min-width: 0;
}

.reply-author {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary-500);
  display: block;
  margin-bottom: var(--space-1);
}

.reply-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 语音消息 */
.voice-message {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-2);
  padding: var(--space-2);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
}

.voice-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.play-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
}

.play-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.voice-duration {
  font-size: var(--font-size-sm);
  color: var(--color-text-inverse);
  font-weight: var(--font-weight-medium);
}

.voice-waveform {
  display: flex;
  align-items: center;
  gap: 2px;
  flex: 1;
}

.wave-bar {
  width: 2px;
  background: var(--color-text-inverse);
  border-radius: var(--radius-full);
  transition: height var(--duration-fast) var(--ease-out);
}

/* 输入区域 */
.input-area {
  padding: var(--space-3) var(--space-4);
  background: var(--color-background-primary);
  border-top: 1px solid var(--color-border-primary);
}

.input-container {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  background: var(--color-background-secondary);
  border-radius: var(--radius-full);
  padding: var(--space-2);
}

.attachment-btn {
  width: var(--button-height-sm);
  height: var(--button-height-sm);
  border: none;
  background: transparent;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  flex-shrink: 0;
}

.attachment-btn:hover {
  background: var(--color-background-tertiary);
  color: var(--color-text-primary);
}

.message-input-wrapper {
  flex: 1;
}

.message-input {
  width: 100%;
  border: none;
  background: transparent;
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  outline: none;
  padding: var(--space-2) 0;
}

.message-input::placeholder {
  color: var(--color-text-tertiary);
}

.send-btn {
  width: var(--button-height-sm);
  height: var(--button-height-sm);
  border: none;
  background: var(--color-primary-500);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-inverse);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
  background: var(--color-primary-600);
  transform: scale(1.05);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 图标样式 */
.back-btn svg {
  width: 24px;
  height: 24px;
  transition: all 0.2s ease-in-out;
}

.back-btn:hover svg {
  width: 26px;
  height: 26px;
}

.action-btn svg {
  width: 20px;
  height: 20px;
  transition: all 0.2s ease-in-out;
}

.action-btn:hover svg {
  width: 22px;
  height: 22px;
}

.play-btn svg {
  width: 16px;
  height: 16px;
}

.attachment-btn svg {
  width: 20px;
  height: 20px;
  transition: all 0.2s ease-in-out;
}

.attachment-btn:hover svg {
  width: 22px;
  height: 22px;
}

.send-btn svg {
  width: 20px;
  height: 20px;
  transition: all 0.2s ease-in-out;
}

.send-btn:hover:not(:disabled) svg {
  width: 22px;
  height: 22px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .message-bubble {
    max-width: 260px;
  }
  
  .chat-header {
    padding: var(--space-2) var(--space-3);
  }
  
  .messages-container {
    padding: var(--space-3);
  }
}
</style>