<template>
  <div class="login">
    <!-- 顶部导航栏 -->
    <header class="app-bar">
      <div class="header-content">
        <h1>登录</h1>
      </div>
    </header>

    <!-- 登录内容区域 -->
    <div class="login-content">
      <!-- 欢迎信息 -->
      <div class="welcome-section">
        <div class="app-logo">
          <div class="logo-icon">
            <MessageCircle :size="48" />
          </div>
        </div>
        <h2 class="welcome-title">欢迎回来</h2>
        <p class="welcome-subtitle">登录您的账户继续聊天</p>
      </div>

      <!-- 登录表单 -->
      <div class="form-section">
        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-group">
            <div class="input-container">
              <User class="input-icon" />
              <input 
                type="text" 
                class="form-input" 
                placeholder="请输入用户名或邮箱"
                v-model="loginForm.username"
              />
            </div>
          </div>
          
          <div class="form-group">
            <div class="input-container">
              <Lock class="input-icon" />
              <input 
                :type="showPassword ? 'text' : 'password'" 
                class="form-input" 
                placeholder="请输入密码"
                v-model="loginForm.password"
              />
              <button 
                type="button" 
                class="password-toggle"
                @click="showPassword = !showPassword"
              >
                <Eye v-if="showPassword" />
                <EyeOff v-else />
              </button>
            </div>
          </div>

          <div class="form-options">
            <label class="remember-me">
              <input type="checkbox" v-model="loginForm.rememberMe" />
              <span class="checkmark"></span>
              <span class="label-text">记住我</span>
            </label>
            <router-link to="/forgot-password" class="forgot-link">
              忘记密码？
            </router-link>
          </div>

          <button type="submit" class="login-btn" :disabled="isLoading">
            <span v-if="!isLoading">登录</span>
            <span v-else class="loading-text">
              <div class="loading-spinner"></div>
              登录中...
            </span>
          </button>
        </form>

        <!-- 注册链接 -->
        <div class="register-section">
          <p class="register-text">
            还没有账户？
            <router-link to="/register" class="register-link">
              立即注册
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { MessageCircle, User, Lock, Eye, EyeOff } from 'lucide-vue-next'
import { showSuccess, showError, showWarning } from '@/utils'

const router = useRouter()

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
  rememberMe: false
})

// 状态管理
const showPassword = ref(false)
const isLoading = ref(false)

// 登录处理
const handleLogin = async () => {
  // 表单验证
  if (!loginForm.value.username.trim()) {
    showWarning('请输入用户名或邮箱')
    return
  }
  
  if (!loginForm.value.password.trim()) {
    showWarning('请输入密码')
    return
  }
  
  if (loginForm.value.password.length < 6) {
    showWarning('密码长度不能少于6位')
    return
  }
  
  isLoading.value = true
  
  try {
    // 这里添加登录逻辑
    await new Promise(resolve => setTimeout(resolve, 1500)) // 模拟API调用
    
    // 模拟登录结果（实际项目中根据API返回结果判断）
    const isSuccess = Math.random() > 0.3 // 70%成功率用于演示
    
    if (isSuccess) {
      showSuccess('登录成功！')
      // 登录成功后跳转到聊天页面
      setTimeout(() => {
        router.push('/chat')
      }, 1000)
    } else {
      throw new Error('用户名或密码错误')
    }
  } catch (error) {
    console.error('登录失败:', error)
    showError(error instanceof Error ? error.message : '登录失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.login {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background-primary);
}

.app-bar {
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
  padding: var(--space-3) var(--space-4);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  box-shadow: var(--shadow-xs);
}

.header-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.app-bar h1 {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  letter-spacing: var(--letter-spacing-tight);
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--space-6) var(--space-4);
  overflow-y: auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: var(--space-10);
}

.app-logo {
  margin-bottom: var(--space-6);
}

.logo-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--space-20);
  height: var(--space-20);
  background: var(--color-primary-500);
  border-radius: var(--radius-2xl);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-lg);
}

.welcome-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0 0 var(--space-2) 0;
  letter-spacing: var(--letter-spacing-tight);
}

.welcome-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.form-section {
  flex: 1;
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

.login-form {
  margin-bottom: var(--space-8);
}

.form-group {
  margin-bottom: var(--space-5);
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: var(--space-4);
  color: var(--color-text-tertiary);
  z-index: 1;
}

.form-input {
  width: 100%;
  height: var(--space-12);
  padding: 0 var(--space-4) 0 var(--space-12);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-lg);
  background: var(--color-background-secondary);
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  outline: none;
  transition: all var(--duration-fast) var(--ease-out);
}

.form-input::placeholder {
  color: var(--color-text-tertiary);
}

.form-input:focus {
  border-color: var(--color-border-focus);
  background: var(--color-background-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.password-toggle {
  position: absolute;
  right: var(--space-4);
  background: none;
  border: none;
  color: var(--color-text-tertiary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-out);
}

.password-toggle:hover {
  color: var(--color-text-secondary);
  background: var(--color-background-tertiary);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.remember-me {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.remember-me input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: var(--space-5);
  height: var(--space-5);
  border: 2px solid var(--color-border-secondary);
  border-radius: var(--radius-sm);
  margin-right: var(--space-2);
  position: relative;
  transition: all var(--duration-fast) var(--ease-out);
}

.remember-me input[type="checkbox"]:checked + .checkmark {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.label-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.forgot-link {
  font-size: var(--font-size-sm);
  color: var(--color-text-link);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-out);
}

.forgot-link:hover {
  color: var(--color-primary-600);
}

.login-btn {
  width: 100%;
  height: var(--space-12);
  background: var(--color-primary-500);
  color: var(--color-text-inverse);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.login-btn:hover:not(:disabled) {
  background: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.login-btn:active:not(:disabled) {
  transform: translateY(0);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.loading-spinner {
  width: var(--space-4);
  height: var(--space-4);
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.register-section {
  text-align: center;
}

.register-text {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
}

.register-link {
  color: var(--color-text-link);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: color var(--duration-fast) var(--ease-out);
}

.register-link:hover {
  color: var(--color-primary-600);
}

/* 图标样式 */
.input-icon {
  width: 20px;
  height: 20px;
}

.password-toggle svg {
  width: 20px;
  height: 20px;
  transition: all 0.2s ease-in-out;
}

.password-toggle:hover svg {
  width: 22px;
  height: 22px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .login-content {
    padding: var(--space-4) var(--space-3);
  }
  
  .welcome-title {
    font-size: var(--font-size-2xl);
  }
  
  .welcome-subtitle {
    font-size: var(--font-size-base);
  }
}
</style>