<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'

interface Props {
  name?: string
  mode?: 'default' | 'out-in' | 'in-out'
  appear?: boolean
  duration?: number
}

const props = withDefaults(defineProps<Props>(), {
  name: 'page',
  mode: 'out-in',
  appear: true,
  duration: 250
})

const router = useRouter()
const route = useRoute()

// 获取路由层级
const getRouteLevel = (routeMeta: any): number => {
  return routeMeta?.level ?? 1
}

// 动画类型 - 默认从右到左滑入
const transitionName = ref('slide-left')

// 监听路由变化，确定动画方向
router.beforeEach((to, from) => {
  if (!from.name) {
    // 首次加载时使用从右到左滑入
    transitionName.value = 'slide-left'
    return
  }

  // 统一使用左右滑动动画，移除特殊页面的动画配置
  // 所有页面切换都使用左右滑入滑出效果

  const toLevel = getRouteLevel(to.meta)
  const fromLevel = getRouteLevel(from.meta)

  // 根据路由层级确定动画方向
  if (toLevel > fromLevel) {
    // 进入更深层级，从右到左滑动
    transitionName.value = 'slide-left'
  } else if (toLevel < fromLevel) {
    // 返回上级，从左到右滑动
    transitionName.value = 'slide-right'
  } else {
    // 同级切换，从右到左滑动
    transitionName.value = 'slide-left'
  }
})

// 动态过渡名称
const currentTransition = computed(() => {
  return `${props.name}-${transitionName.value}`
})
</script>

<template>
  <Transition 
    :name="currentTransition"
    :mode="mode"
    :appear="appear"
    :duration="duration"
  >
    <slot />
  </Transition>
</template>

<style scoped>
/* 页面过渡动画组件样式 */
/* 主要样式已移至 src/styles/page-transitions.css */

/* 组件特定的样式可以在这里添加 */
</style>