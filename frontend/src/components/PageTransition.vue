<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

interface Props {
  name?: string
  mode?: 'default' | 'out-in' | 'in-out'
  appear?: boolean
  duration?: number
}

const props = withDefaults(defineProps<Props>(), {
  name: 'page',
  mode: 'out-in',
  appear: true,
  duration: 250
})

const router = useRouter()
const route = useRoute()

// 获取路由层级
const getRouteLevel = (routeMeta: any): number => {
  return routeMeta?.level ?? 1
}

// 动画类型 - 默认从右到左滑入
const transitionName = ref('slide-left')

// 监听路由变化，更新动画方向
watch(() => route.path, (newPath, oldPath) => {
  if (!oldPath) {
    transitionName.value = 'slide-left'
    return
  }

  // 获取路由配置
  const routes = router.getRoutes()

  const currentRouteConfig = routes.find(r => r.path === newPath)
  const previousRouteConfig = routes.find(r => r.path === oldPath)

  const toLevel = getRouteLevel(currentRouteConfig?.meta)
  const fromLevel = getRouteLevel(previousRouteConfig?.meta)

  // 根据路由层级确定动画方向
  if (toLevel > fromLevel) {
    // 进入更深层级，从右到左滑动
    transitionName.value = 'slide-left'
  } else if (toLevel < fromLevel) {
    // 返回上级，从左到右滑动
    transitionName.value = 'slide-right'
  } else {
    // 同级切换，从右到左滑动
    transitionName.value = 'slide-left'
  }

  console.log(`页面切换: ${oldPath} -> ${newPath}, 动画: ${transitionName.value}`)
}, { immediate: true })

// 动态过渡名称
const currentTransition = computed(() => {
  return `${props.name}-${transitionName.value}`
})
</script>

<template>
  <Transition 
    :name="currentTransition"
    :mode="mode"
    :appear="appear"
    :duration="duration"
  >
    <slot />
  </Transition>
</template>

<style scoped>
/* 页面过渡动画组件样式 */
/* 主要样式已移至 src/styles/page-transitions.css */

/* 确保过渡容器有正确的定位 */
:deep(.v-enter-active),
:deep(.v-leave-active) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
</style>