import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    redirect: '/chat'
  },
  {
    path: '/chat',
    name: 'Chat',
    component: () => import('@/views/Chat/ChatList.vue'),
    meta: { 
      title: '消息',
      level: 1,
      keepAlive: true
    }
  },
  {
    path: '/chat/:id',
    name: 'ChatDetail',
    component: () => import('@/views/Chat/ChatDetail.vue'),
    meta: { 
      title: '聊天',
      level: 2,
      keepAlive: true
    }
  },
  {
    path: '/contacts',
    name: 'Contacts',
    component: () => import('@/views/Contacts/ContactList.vue'),
    meta: { 
      title: '联系人',
      level: 1,
      keepAlive: true
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile/UserProfile.vue'),
    meta: { 
      title: '我的',
      level: 1
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Auth/Login.vue'),
    meta: { 
      title: '登录',
      level: 0,
      transition: 'fade'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Auth/Register.vue'),
    meta: { 
      title: '注册',
      level: 0,
      transition: 'slide-up'
    }
  },
  {
    path: '/demo/toast',
    name: 'ToastDemo',
    component: () => import('@/views/Demo/ToastDemo.vue'),
    meta: { 
      title: 'Toast演示',
      level: 2,
      transition: 'scale'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }
  
  // 这里可以添加认证逻辑
  next()
})

export default router