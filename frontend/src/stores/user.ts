import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface User {
  id: string
  username: string
  nickname: string
  avatar?: string
  status: 'online' | 'offline' | 'away'
  lastSeen?: Date
}

export const useUserStore = defineStore('user', () => {
  const currentUser = ref<User | null>(null)
  const isLoggedIn = ref(false)
  const token = ref<string | null>(null)

  // 登录
  const login = async (username: string, password: string) => {
    try {
      // TODO: 调用登录API
      console.log('登录:', { username, password })
      
      // 模拟登录成功
      currentUser.value = {
        id: '1',
        username,
        nickname: username,
        status: 'online'
      }
      isLoggedIn.value = true
      token.value = 'mock-token'
      
      return { success: true }
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, error: '登录失败' }
    }
  }

  // 注册
  const register = async (username: string, nickname: string, password: string) => {
    try {
      // TODO: 调用注册API
      console.log('注册:', { username, nickname, password })
      
      // 模拟注册成功
      return { success: true }
    } catch (error) {
      console.error('注册失败:', error)
      return { success: false, error: '注册失败' }
    }
  }

  // 登出
  const logout = () => {
    currentUser.value = null
    isLoggedIn.value = false
    token.value = null
  }

  // 更新用户信息
  const updateProfile = (updates: Partial<User>) => {
    if (currentUser.value) {
      currentUser.value = { ...currentUser.value, ...updates }
    }
  }

  return {
    currentUser,
    isLoggedIn,
    token,
    login,
    register,
    logout,
    updateProfile
  }
}, {
  persist: {
    key: 'user-store',
    storage: localStorage
  }
})