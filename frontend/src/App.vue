<script setup lang="ts">
import { useRoute } from 'vue-router'
import { computed, ref } from 'vue'
import { MessageCircle, Users } from 'lucide-vue-next'
import PageTransition from '@/components/PageTransition.vue'

const route = useRoute()

// 未读消息数量
const unreadCount = ref(3)

// 需要显示底部导航的页面
const showTabBar = computed(() => {
  const tabRoutes = ['/chat', '/contacts']
  return tabRoutes.includes(route.path)
})


</script>

<template>
  <div id="app">
    <!-- 主要内容区域 -->
    <div class="main-content" :class="{ 'has-bottom-nav': showTabBar }">
      <router-view v-slot="{ Component, route }">
        <PageTransition>
          <KeepAlive :include="['ChatList', 'ContactList', 'ChatDetail']">
            <component :is="Component" :key="route.path" />
          </KeepAlive>
        </PageTransition>
      </router-view>
    </div>
    
    <!-- 底部导航栏 -->
    <nav v-if="showTabBar" class="bottom-navigation">
      <router-link to="/chat" class="nav-item" :class="{ active: $route.path === '/chat' }">
        <div class="nav-icon">
          <MessageCircle :size="24" />
        </div>
        <span class="nav-label">Chats</span>
        <div v-if="unreadCount > 0" class="badge">{{ unreadCount }}</div>
      </router-link>
      <router-link to="/contacts" class="nav-item" :class="{ active: $route.path === '/contacts' }">
        <div class="nav-icon">
          <Users :size="24" />
        </div>
        <span class="nav-label">Contacts</span>
      </router-link>
    </nav>
  </div>
</template>

<style scoped>
#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background-secondary);
  position: relative;
  overflow: hidden;
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* 有底部导航栏时为主内容添加底部间距 */
.main-content.has-bottom-nav {
  padding-bottom: var(--space-20);
}

.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--space-20);
  background: var(--color-background-primary);
  border-top: 1px solid var(--color-border-primary);
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: var(--z-index-fixed);
  box-shadow: var(--shadow-lg);
}

.nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--color-text-secondary);
  padding: var(--space-3) var(--space-5);
  transition: all var(--duration-normal) var(--ease-out);
  border-radius: var(--radius-lg);
  min-width: var(--space-15);
}

.nav-item.active {
  color: var(--color-primary-500);
  background: var(--color-primary-50);
}

.nav-item:hover {
  color: var(--color-primary-500);
  transform: translateY(-2px);
}

.nav-icon {
  margin-bottom: var(--space-1);
  transition: transform var(--duration-normal) var(--ease-out);
}

.nav-item.active .nav-icon {
  transform: scale(1.1);
}

.nav-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  letter-spacing: var(--letter-spacing-wide);
}

.badge {
  position: absolute;
  top: var(--space-2);
  right: var(--space-3);
  background: var(--color-error);
  color: var(--color-text-inverse);
  border-radius: var(--radius-lg);
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  min-width: var(--space-4);
  height: var(--space-4);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

/* 确保页面内容不被底部导航栏遮挡 */
:deep(.main-content > *) {
  height: 100%;
  overflow-y: auto;
}

/* 页面过渡动画容器样式 */
.main-content {
  position: relative;
}

.main-content > * {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

</style>
