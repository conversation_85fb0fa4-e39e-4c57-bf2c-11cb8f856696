/* 页面过渡动画样式 */
/* 使用设计系统的 CSS 变量确保一致性 */

/* =================================
   基础过渡动画样式
   ================================= */

/* 滑动动画 - 向左进入（前进） */
.page-slide-left-enter-active,
.page-slide-left-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  will-change: transform, opacity;
}

.page-slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.page-slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

/* 滑动动画 - 向右进入（后退） */
.page-slide-right-enter-active,
.page-slide-right-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  will-change: transform, opacity;
}

.page-slide-right-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.page-slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

/* 淡入淡出动画 */
.page-fade-enter-active,
.page-fade-leave-active {
  transition: opacity var(--duration-fast) var(--ease-out);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.page-fade-enter-from,
.page-fade-leave-to {
  opacity: 0;
}

/* 向上滑动动画（适合模态页面） */
.page-slide-up-enter-active,
.page-slide-up-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  will-change: transform, opacity;
}

.page-slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.page-slide-up-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

/* 向下滑动动画 */
.page-slide-down-enter-active,
.page-slide-down-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  will-change: transform, opacity;
}

.page-slide-down-enter-from {
  transform: translateY(-100%);
  opacity: 0;
}

.page-slide-down-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

/* 缩放动画（适合弹窗类页面） */
.page-scale-enter-active,
.page-scale-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  will-change: transform, opacity;
}

.page-scale-enter-from {
  transform: scale(0.9);
  opacity: 0;
}

.page-scale-leave-to {
  transform: scale(1.1);
  opacity: 0;
}

/* 弹性缩放动画 */
.page-scale-bounce-enter-active,
.page-scale-bounce-leave-active {
  transition: all var(--duration-slow) var(--ease-bounce);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  will-change: transform, opacity;
}

.page-scale-bounce-enter-from {
  transform: scale(0.3);
  opacity: 0;
}

.page-scale-bounce-leave-to {
  transform: scale(1.2);
  opacity: 0;
}

/* =================================
   高级过渡动画效果
   ================================= */

/* 旋转滑动动画 */
.page-rotate-slide-enter-active,
.page-rotate-slide-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  will-change: transform, opacity;
  transform-origin: center center;
}

.page-rotate-slide-enter-from {
  transform: translateX(100%) rotate(10deg);
  opacity: 0;
}

.page-rotate-slide-leave-to {
  transform: translateX(-100%) rotate(-10deg);
  opacity: 0;
}

/* 翻转动画 */
.page-flip-enter-active,
.page-flip-leave-active {
  transition: all var(--duration-slow) var(--ease-out);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  will-change: transform, opacity;
  transform-origin: center center;
  backface-visibility: hidden;
}

.page-flip-enter-from {
  transform: rotateY(-90deg);
  opacity: 0;
}

.page-flip-leave-to {
  transform: rotateY(90deg);
  opacity: 0;
}

/* =================================
   性能优化和响应式支持
   ================================= */

/* 为所有过渡动画元素添加基础优化 */
[class*="page-"][class*="-enter-active"],
[class*="page-"][class*="-leave-active"] {
  /* 确保动画在独立的合成层运行 */
  will-change: transform, opacity;
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 确保元素完全覆盖容器 */
  width: 100%;
  height: 100%;
}

/* 解决 iOS Safari 中的闪烁问题 */
[class*="page-"][class*="-enter-active"],
[class*="page-"][class*="-leave-active"] {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
}

/* =================================
   无障碍支持
   ================================= */

/* 为用户偏好减少动画时提供支持 */
@media (prefers-reduced-motion: reduce) {
  [class*="page-"][class*="-enter-active"],
  [class*="page-"][class*="-leave-active"] {
    transition: none !important;
    animation: none !important;
    transform: none !important;
    opacity: 1 !important;
  }
  
  [class*="page-"][class*="-enter-from"],
  [class*="page-"][class*="-leave-to"] {
    transform: none !important;
    opacity: 1 !important;
  }
}

/* =================================
   移动端优化
   ================================= */

/* 在小屏幕设备上使用更快的动画 */
@media (max-width: 768px) {
  [class*="page-"][class*="-enter-active"],
  [class*="page-"][class*="-leave-active"] {
    transition-duration: var(--duration-fast);
  }
}

/* 高分辨率设备优化 */
@media (-webkit-min-device-pixel-ratio: 2) {
  [class*="page-"][class*="-enter-active"],
  [class*="page-"][class*="-leave-active"] {
    /* 在高分辨率设备上启用子像素渲染 */
    -webkit-font-smoothing: subpixel-antialiased;
  }
}

/* =================================
   调试和开发辅助
   ================================= */

/* 开发环境下显示过渡边界（可通过 data-debug 属性控制） */
[data-debug="true"] [class*="page-"][class*="-enter-active"],
[data-debug="true"] [class*="page-"][class*="-leave-active"] {
  border: 2px solid var(--color-primary-500);
  background: rgba(0, 122, 255, 0.1);
}

/* 慢动画模式（用于调试） */
[data-slow-transitions="true"] [class*="page-"][class*="-enter-active"],
[data-slow-transitions="true"] [class*="page-"][class*="-leave-active"] {
  transition-duration: 2s !important;
}