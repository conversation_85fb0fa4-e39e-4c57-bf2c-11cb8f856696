"""User schemas."""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, Field, field_validator


class UserBase(BaseModel):
    """Base user schema with common fields."""
    
    username: str = Field(..., min_length=3, max_length=50, description="Username")
    nickname: Optional[str] = Field(None, max_length=50, description="Display nickname")
    avatar_url: Optional[str] = Field(None, max_length=255, description="Avatar URL")
    signature: Optional[str] = Field(None, max_length=500, description="User signature")
    
    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        """Validate username format."""
        if not v.isalnum() and '_' not in v:
            raise ValueError('Username must contain only alphanumeric characters and underscores')
        return v.lower()


class UserCreate(UserBase):
    """Schema for creating a new user."""
    
    password: str = Field(..., min_length=8, max_length=128, description="Password")
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class UserRegister(BaseModel):
    """Schema for user registration."""
    
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=8, max_length=128)
    nickname: Optional[str] = Field(None, max_length=50)
    
    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        """Validate username format."""
        if not v.replace('_', '').isalnum():
            raise ValueError('Username must contain only alphanumeric characters and underscores')
        return v.lower()
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v


class UserLogin(BaseModel):
    """Schema for user login."""
    
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")


class UserUpdate(BaseModel):
    """Schema for updating user information."""
    
    nickname: Optional[str] = Field(None, max_length=50)
    avatar_url: Optional[str] = Field(None, max_length=255)
    signature: Optional[str] = Field(None, max_length=500)


class UserInDB(UserBase):
    """Schema for user data stored in database."""
    
    id: int
    password_hash: str
    is_active: bool = True
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class User(UserBase):
    """Schema for user data returned to clients."""
    
    id: int
    is_active: bool = True
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
    
    @property
    def display_name(self) -> str:
        """Return the display name (nickname if available, otherwise username)."""
        return self.nickname or self.username


class UserProfile(User):
    """Extended user schema with additional profile information."""
    
    friends_count: Optional[int] = 0
    chats_count: Optional[int] = 0
    messages_count: Optional[int] = 0


class UserSearch(BaseModel):
    """Schema for user search results."""
    
    id: int
    username: str
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    is_friend: bool = False
    friendship_status: Optional[str] = None  # pending, accepted, blocked
    
    class Config:
        from_attributes = True
    
    @property
    def display_name(self) -> str:
        """Return the display name."""
        return self.nickname or self.username


class PasswordChange(BaseModel):
    """Schema for changing password."""
    
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, max_length=128, description="New password")
    
    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v):
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v